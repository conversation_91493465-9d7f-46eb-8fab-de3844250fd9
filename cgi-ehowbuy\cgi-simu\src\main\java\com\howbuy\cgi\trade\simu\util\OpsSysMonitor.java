package com.howbuy.cgi.trade.simu.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.date.DateUtil;
import com.howbuy.trace.RequestChainTrace;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 运维系统监控告警
 */
public class OpsSysMonitor {

    private static Logger logger =  LoggerFactory.getLogger(OpsSysMonitor.class);
    private static Logger warnLogger = null;
    static{
        try {
            warnLogger = LoggerFactory.getLogger("monitorjson");
        } catch (Exception e) {
                        
        }
    }

    public static final String ERROR = "ERROR";
    public static final String WARN = "WARN";
    public static final String INFO = "INFO";

    /**
     * 监控告警
     */
    public static void warn(String content, String level) {
        String reqId = RequestChainTrace.getReqId();
        String remoteHost = RequestChainTrace.getRemoteHost();
        RequestChainTrace.buildAndSet(reqId, remoteHost);
        String currentRanNo = RequestChainTrace.getRanNo();
        ThreadContext.put("uuid", reqId);
        ThreadContext.put("ranNo", currentRanNo);
        Map<String, String> warnMsg = new HashMap<>();
        warnMsg.put("time", DateUtil.formatToString(new Date(), DateUtil.YYYYMMDDHHMMssSSS));
        warnMsg.put("msg", content);
        warnMsg.put("level", level);
        warnMsg.put("host", getIp());
        warnMsg.put("traceId", reqId);
        warnMsg.put("appName", "cgi-simu-container");
        warnMsg.put("noticeType", "toSys");
        String jsonMsg = JSON.toJSONString(warnMsg);
        logger.info("OpsMonitor-warn,告警,jsonMsg={}", jsonMsg);
        warnLogger.warn(jsonMsg);
    }


    /**
     * 监控告警
     */
    public static void businessWarn(String content, String level) {
        String reqId = RequestChainTrace.getReqId();
        String remoteHost = RequestChainTrace.getRemoteHost();
        RequestChainTrace.buildAndSet(reqId, remoteHost);
        String currentRanNo = RequestChainTrace.getRanNo();
        ThreadContext.put("uuid", reqId);
        ThreadContext.put("ranNo", currentRanNo);
        Map<String, String> warnMsg = new HashMap<>();
        warnMsg.put("time", DateUtil.formatToString(new Date(), DateUtil.YYYYMMDDHHMMssSSS));
        warnMsg.put("msg", content);
        warnMsg.put("level", level);
        warnMsg.put("host", getIp());
        warnMsg.put("traceId", reqId);
        warnMsg.put("appName", "cgi-simu-container");
        warnMsg.put("noticeType", "toBusiness");
        String jsonMsg = JSON.toJSONString(warnMsg);
        logger.info("OpsMonitor-warn,告警,jsonMsg={}", jsonMsg);
        warnLogger.warn(jsonMsg);
    }

    private static String getIp() {
        String result = "";
        try {
            // 获取IP地址
            result = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            logger.error("Exception：get Ip failure ", e);
        }
        return result;
    }

    public static String convertLog(String traceId, String txCode, String returnCode, String custNo, long costTime, String remoteHost) {
        JSONObject json = new JSONObject();
        json.put("time", getDetailTimeStr());
        json.put("tx_code", txCode);
        json.put("costs", costTime);
        json.put("return_code", returnCode);
        json.put("traceId", traceId);
        json.put("custNo", custNo);
        json.put("remoteHost", remoteHost);
        return json.toJSONString();
    }

    private static String getDetailTimeStr() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
    }

}
