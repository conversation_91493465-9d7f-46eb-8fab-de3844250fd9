<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/cgi-simu-container</property>
		<property name="rollingLogName">cgi-simu-container</property>
		<property name="monitorName">warn</property>
		<property name="guomiLogName">howbuy-cgi-guomi</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout>
				<pattern>
					{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"${ctx:custNo}","hboneNo":"${ctx:hboneNo}","labelName":"%markerSimpleName","PtxId":"%X{PtxId}","SpanId":"%X{PspanId}","concatId":"${ctx:concatId}","tid":"${ctx:traceId}","sid":"${ctx:sceneId}","pid":"${ctx:pageId}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg%throwable}{JSON}"}%n
				</pattern>
			</PatternLayout>
		</Console>

		<RollingFile name="AppLog" filename="${logPath}/cgi-simu-container.log" filepattern="${logPath}/cgi-simu-container-%d{yyyyMMdd-HH}.log">
			<CustomPatternLayout>
				<pattern>
					{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"${ctx:custNo}","hboneNo":"${ctx:hboneNo}","labelName":"%markerSimpleName","PtxId":"%X{PtxId}","SpanId":"%X{PspanId}","concatId":"${ctx:concatId}","tid":"${ctx:traceId}","sid":"${ctx:sceneId}","pid":"${ctx:pageId}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg%throwable}{JSON}"}%n
				</pattern>
				<replaces>
					<replace regex='(\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****" />
					<replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"mobileBank\\":\\"|mobileNo=)(\d{7})\d{4}' replacement="$1$2****" />
					<replace regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankAcctFull\\":\\"|bankAcct=)(\d{1,})(\d{6})' replacement="$1******$3" />
					<replace regex='(\\"custName\\":\\"|\\"userName\\":\\"|\\"bankAcctName\\":\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})' replacement="$1$2*$3" />
					<replace regex='(\\"address\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"' />
					<replace regex='(\\"addr\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"' />
					<replace regex='(\\\\\\\"idNo\\\\\\\":\\\\\\\")(\w{14})\w{4}' replacement="$1$2****" />
					<replace regex='(\\\\\\\"mobile\\\\\\\":\\\\\\\"|\\\\\\\"mobileNo\\\\\\\":\\\\\\\"|\\\\\\\"mobileBank\\\\\\\":\\\\\\\")(\d{7})\d{4}' replacement="$1$2****" />
					<replace regex='(\\\\\\\"bankAcct\\\\\\\":\\\\\\\"|\\\\\\\"bankNo\\\\\\\":\\\\\\\"|\\\\\\\"bankAcctFull\\\\\\\":\\\\\\\")(\d{1,})(\d{6})' replacement="$1******$3" />
					<replace regex='(\\\\\\\"custName\\\\\\\":\\\\\\\"|\\\\\\\"userName\\\\\\\":\\\\\\\"|\\\\\\\"bankAcctName\\\\\\\":\\\\\\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})' replacement="$1$2*$3" />
					<replace regex='(\\\\\\\"address\\\\\\\":\\\\\\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"' />
					<replace regex='(\\\\\\\"addr\\\\\\\":\\\\\\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\\\\\\"' />
				</replaces>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="3" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="AlarmLog" filename="${logPath}/cgi-simu-container-alarm.log" filepattern="${logPath}/%d{yyyyMMdd}/cgi-simu-container-alarm.log">
			<CustomPatternLayout>
				<pattern>
					{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"${ctx:custNo}","hboneNo":"${ctx:hboneNo}","labelName":"%markerSimpleName","PtxId":"%X{PtxId}","SpanId":"%X{PspanId}","concatId":"${ctx:concatId}","tid":"${ctx:traceId}","sid":"${ctx:sceneId}","pid":"${ctx:pageId}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg%throwable}{JSON}"}%n
				</pattern>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="1024MB"/>
			</Policies>
			<DefaultRolloverStrategy max="50" />
		</RollingFile>

		<RollingFile name="MainLog" filename="${logPath}/cgi-simu-container-main.log" filepattern="${logPath}/%d{yyyyMMdd}/cgi-simu-container-main.log">
			<CustomPatternLayout>
				<PatternLayout pattern="%msg%n" />
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="1024MB"/>
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="OtherLog" filename="${logPath}/cgi-simu-container-other.log" filepattern="${logPath}/%d{yyyyMMdd}/cgi-simu-container-other.log">
			<CustomPatternLayout>
				<pattern>
					{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"${ctx:custNo}","hboneNo":"${ctx:hboneNo}","labelName":"%markerSimpleName","PtxId":"%X{PtxId}","SpanId":"%X{PspanId}","concatId":"${ctx:concatId}","tid":"${ctx:traceId}","sid":"${ctx:sceneId}","pid":"${ctx:pageId}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg%throwable}{JSON}"}%n
				</pattern>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="1024MB"/>
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		<RollingFile name="monitorLogger" filename="${logPath}/${monitorName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${monitorName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		<!-- 独立ERROR日志文件 -->
		<RollingFile name="ErrorLog" filename="${logPath}/cgi-simu-container-error.log" filepattern="${logPath}/%d{yyyyMMdd}/cgi-simu-container-error.log">
			<PatternLayout>
				<pattern>
					{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"${ctx:custNo}","hboneNo":"${ctx:hboneNo}","labelName":"%markerSimpleName","PtxId":"%X{PtxId}","SpanId":"%X{PspanId}","concatId":"${ctx:concatId}","tid":"${ctx:traceId}","sid":"${ctx:sceneId}","pid":"${ctx:pageId}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg%throwable}{JSON}"}%n
				</pattern>
			</PatternLayout>
			<!-- 关键：仅捕获ERROR及以上级别 -->
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true"/>
				<SizeBasedTriggeringPolicy size="1024MB"/>
			</Policies>
			<DefaultRolloverStrategy max="30"/>
		</RollingFile>



		<RollingFile name="ErrorMainLogger" filename="${logPath}/howbuy-cgi-error-message.log" filepattern="${logPath}/%d{yyyyMMdd}/howbuy-cgi-error-message.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="50" />
		</RollingFile>

		<RollingFile name="GuoMiLogger" filename="${logPath}/${guomiLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${guomiLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="50" />
		</RollingFile>
	</Appenders>

	<Loggers>
		<AsyncLogger level="error" additivity="false">
			<appender-ref ref="ErrorLog" />
			<!-- 确保仅接受 ERROR 级别 -->
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
		</AsyncLogger>
		<logger name="monitorjson" level="info" additivity="false">
			<appender-ref ref="monitorLogger"/>
		</logger>
		<AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="info" additivity="false">
			<AppenderRef ref="OtherLog" />
		</AsyncLogger>
		<AsyncLogger name="com.alibaba" level="info" additivity="false">
			<appender-ref ref="OtherLog" />
		</AsyncLogger>
		<AsyncLogger name="org.apache" level="info" additivity="false">
			<appender-ref ref="OtherLog" />
		</AsyncLogger>
		<Logger name="com.howbuy.cachemanagement" level="warn">
			<AppenderRef ref="OtherLog" />
		</Logger>
		<Logger name="com.howbuy.message" level="warn">
			<AppenderRef ref="OtherLog" />
		</Logger>
		<Logger name="com.howbuy.session" level="warn">
			<AppenderRef ref="OtherLog" />
		</Logger>
		<Logger name="com.howbuy.trade.common.DebugManager" level="error">
			<AppenderRef ref="OtherLog" />
		</Logger>
		<AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="error">
			<AppenderRef ref="OtherLog" />
		</AsyncLogger>
		<AsyncLogger name="com.alibaba.nacos" level="error">
			<appender-ref ref="OtherLog" />
		</AsyncLogger>
		<Logger name="com.howbuy.trade.common.basecommon.remote.TradeDubboFilter" level="info" additivity="false">
			<AppenderRef ref="AppLog" />
		</Logger>

		<Root level="info">
			<AppenderRef ref="AppLog" />
			<appender-ref ref="ErrorLog" />
		</Root>
		<AsyncLogger name="alarmLog" level="info" additivity="false">
			<appender-ref ref="AlarmLog"/>
		</AsyncLogger>
		<AsyncLogger name="mainLog" level="info" additivity="false">
			<appender-ref ref="MainLog"/>
		</AsyncLogger>
		<AsyncLogger name="errorMainlog" level="info" additivity="false">
			<appender-ref ref="ErrorMainLogger"/>
		</AsyncLogger>
		<AsyncLogger name="guoMilog" level="info" additivity="false">
			<appender-ref ref="GuoMiLogger"/>
		</AsyncLogger>

	</Loggers>

</Configuration>